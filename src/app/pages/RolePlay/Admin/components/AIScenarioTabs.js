import React, {useState, useEffect, forwardRef, useImperativeHandle, useMemo, useCallback, useRef} from 'react';
import {useTranslation} from 'react-i18next';
import {
  Tabs,
  Card,
  Form,
  Input,
  Select,
  InputNumber,
  Checkbox,
  Row,
  Col,
  Button,
  Space,
  Typography,
  message,
  Divider,
  Badge,
  Tooltip,
  Alert,
} from 'antd';
import {
  PlusOutlined,
  UserOutlined,
  FileTextOutlined,
  SettingOutlined,
  RobotOutlined,
} from '@ant-design/icons';

import {BUTTON} from '@constant';
import AntButton from '@component/AntButton';
import {AntForm} from '@component/AntForm';
import {toast} from '@component/ToastProvider';
import {confirm} from '@component/ConfirmProvider';

import {
  getScenariosByCourse,
  createAIScenario,
  updateAIScenario,
  deleteAIScenario,
} from '@services/RolePlay/AIScenarioService';
import {getAllAIPersonasWithoutPagination} from '@services/RolePlay/AIPersonaService';
import {getAllInstruction} from '@services/RolePlay/RolePlayInstructionService';

import {AIPersonaSetupCard} from '../../AIPersona/AIPersonaSetupCard';
import {TaskEvaluationDisplayCard} from '../../Tasks/TaskEvaluationDisplayCard';
import CreateAIPersonaModal from './CreateAIPersonaModal';
import EditAIPersonaModal from './EditAIPersonaModal';

import './AIScenarioTabs.scss';

const {TextArea} = Input;
const {Option} = Select;
const {Text} = Typography;

const AIScenarioTabs = forwardRef(({
                                     courseId,
                                     onScenariosChange,
                                     selectedSimulationType,
                                     onTaskAdd,
                                     onTaskUpdate,
                                     onTaskDelete,
                                   }, ref) => {
  const {t} = useTranslation();
  const [form] = Form.useForm();

  const [scenarios, setScenarios] = useState([]);
  const [activeTabKey, setActiveTabKey] = useState('');
  const [loading, setLoading] = useState(false);

  // Form data
  const [aiPersonas, setAiPersonas] = useState([]);
  const [roleplayInstructions, setRoleplayInstructions] = useState([]);
  const [selectedPersonaForPreview, setSelectedPersonaForPreview] = useState(null);

  // Modal states
  const [isCreatePersonaModalOpen, setIsCreatePersonaModalOpen] = useState(false);
  const [isEditPersonaModalOpen, setIsEditPersonaModalOpen] = useState(false);
  const [editingPersona, setEditingPersona] = useState(null);

  // Tab editing - loại bỏ vì không cho phép edit tên tab trực tiếp

  // Force re-render when tasks change
  const [taskUpdateTrigger, setTaskUpdateTrigger] = useState(0);

  // Ref for TaskEvaluationDisplayCard to trigger AI modal
  const taskCardRef = useRef(null);

  useEffect(() => {
    if (courseId) {
      fetchFormData();
      fetchScenarios();
    }
  }, [courseId]);

  useEffect(() => {
    // Update form when active tab changes
    if (activeTabKey && scenarios.length > 0) {
      const currentScenario = scenarios.find(s => s._id === activeTabKey);
      if (currentScenario) {
        updateFormWithScenario(currentScenario);
      }
    }
  }, [activeTabKey, scenarios]);

  const fetchFormData = async () => {
    try {
      const [personasResponse, instructionsResponse] = await Promise.all([
        getAllAIPersonasWithoutPagination({}, [], false),
        getAllInstruction({}),
      ]);

      if (personasResponse) {
        setAiPersonas(personasResponse);
        console.log('AI Personas loaded:', personasResponse.length);
      }
      if (instructionsResponse) {
        setRoleplayInstructions(instructionsResponse);
        console.log('Roleplay Instructions loaded:', instructionsResponse.length);
      }
    } catch (error) {
      console.error('Error fetching form data:', error);
      toast.error(t('ERROR_FETCHING_FORM_DATA'));
    }
  };

  const fetchScenarios = async () => {
    setLoading(true);
    try {
      const response = await getScenariosByCourse(courseId, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
      if (response && response.length > 0) {
        setScenarios(response);
        // Set active tab to first scenario
        setActiveTabKey(response[0]._id);
        if (onScenariosChange) {
          onScenariosChange(response);
        }
      } else {
        // Create first empty scenario if none exist
        createFirstScenario();
      }
    } catch (error) {
      console.error('Error fetching scenarios:', error);
      createFirstScenario();
    } finally {
      setLoading(false);
    }
  };

  const createFirstScenario = () => {
    const newScenario = {
      _id: `temp_${Date.now()}`,
      name: t('SCENARIO') + ' 1',
      description: '',
      courseId,
      isNew: true,
      taskIds: [],
    };
    setScenarios([newScenario]);
    setActiveTabKey(newScenario._id);
    form.resetFields();
  };

  const updateFormWithScenario = (scenario) => {
    if (!scenario) return;

    const formValues = {
      name: scenario.name || '',
      description: scenario.description || '',
      aiPersonaId: scenario.aiPersonaId?._id || scenario.aiPersonaId,
      roleplayInstructionId: scenario.roleplayInstructionId?._id || scenario.roleplayInstructionId,
      taskIds: scenario.taskIds?.map(task => typeof task === 'object' ? task._id : task) || [],
      estimatedCallTimeInMinutes: scenario.estimatedCallTimeInMinutes || 15,
    };

    form.setFieldsValue(formValues);

    // Update selected persona for preview
    if (scenario.aiPersonaId) {
      if (typeof scenario.aiPersonaId === 'object') {
        setSelectedPersonaForPreview(scenario.aiPersonaId);
      } else {
        // Find persona by ID
        const persona = aiPersonas.find(p => p._id === scenario.aiPersonaId);
        setSelectedPersonaForPreview(persona || null);
      }
    } else {
      setSelectedPersonaForPreview(null);
    }
  };

  const handleAiPersonaChange = (value) => {
    if (value) {
      const selected = aiPersonas.find(p => p._id === value);
      if (selected) {
        setSelectedPersonaForPreview(selected);
        console.log('Selected AI Persona:', selected);
      } else {
        console.warn('AI Persona not found in list:', value);
        setSelectedPersonaForPreview(null);
      }
    } else {
      setSelectedPersonaForPreview(null);
    }

    // Update current scenario
    updateCurrentScenario({aiPersonaId: value});
  };

  const updateCurrentScenario = (updates) => {
    console.log('=== UPDATE CURRENT SCENARIO ===');
    console.log('Active tab key:', activeTabKey);
    console.log('Updates:', updates);

    setScenarios(prev => {
      console.log('Previous scenarios:', prev);
      const updated = prev.map(scenario => {
        if (scenario._id === activeTabKey) {
          const updatedScenario = {...scenario, ...updates, isModified: true};
          console.log('Updated scenario:', updatedScenario);
          return updatedScenario;
        }
        return scenario;
      });
      console.log('New scenarios after update:', updated);
      return updated;
    });
  };

  const handleAddScenario = () => {
    const newScenario = {
      _id: `temp_${Date.now()}`,
      name: `${t('SCENARIO')} ${scenarios.length + 1}`,
      description: '',
      courseId,
      isNew: true,
      taskIds: [],
    };

    setScenarios(prev => [...prev, newScenario]);
    setActiveTabKey(newScenario._id);
  };

  const handleTabEdit = (targetKey, action) => {
    console.log('handleTabEdit called:', { targetKey, action });
    if (action === 'add') {
      handleAddScenario();
    } else if (action === 'remove') {
      handleDeleteScenario(targetKey);
    }
  };

  const handleDeleteScenario = (scenarioId) => {
    console.log('handleDeleteScenario called:', { scenarioId, scenarios });
    const scenario = scenarios.find(s => s._id === scenarioId);
    if (!scenario) {
      console.log('Scenario not found:', scenarioId);
      return;
    }

    // Không cho phép xóa nếu chỉ còn 1 scenario
    if (scenarios.length <= 1) {
      toast.error(t('CANNOT_DELETE_LAST_SCENARIO', 'Không thể xóa kịch bản cuối cùng'));
      return;
    }

    // Nếu là scenario mới (chưa lưu), xóa trực tiếp
    if (scenario.isNew) {
      const updatedScenarios = scenarios.filter(s => s._id !== scenarioId);
      setScenarios(updatedScenarios);

      // Chuyển sang tab khác nếu đang ở tab bị xóa
      if (activeTabKey === scenarioId) {
        setActiveTabKey(updatedScenarios[0]._id);
      }
      return;
    }

    // Hiển thị confirmation modal cho scenario đã lưu
    confirm.delete({
      title: t('CONFIRM_DELETE_SCENARIO', 'Xác nhận xóa kịch bản'),
      content: t('DELETE_SCENARIO_CONFIRMATION', 'Bạn có chắc chắn muốn xóa kịch bản "{name}"? Hành động này không thể hoàn tác.', { name: scenario.name }),
      okText: t('DELETE', 'Xóa'),
      cancelText: t('CANCEL', 'Hủy'),
      handleConfirm: async () => {
        try {
          await deleteAIScenario(scenarioId, false);
          toast.success(t('DELETE_SCENARIO_SUCCESS', 'Xóa kịch bản thành công'));

          // Cập nhật danh sách scenarios
          const updatedScenarios = scenarios.filter(s => s._id !== scenarioId);
          setScenarios(updatedScenarios);

          // Chuyển sang tab khác nếu đang ở tab bị xóa
          if (activeTabKey === scenarioId) {
            setActiveTabKey(updatedScenarios[0]._id);
          }

          // Thông báo cho parent component
          if (onScenariosChange) {
            onScenariosChange(updatedScenarios);
          }
        } catch (error) {
          console.error('Error deleting scenario:', error);
          toast.error(t('DELETE_SCENARIO_ERROR', 'Có lỗi xảy ra khi xóa kịch bản'));
        }
      },
    });
  };

  // Loại bỏ các function edit tab name vì không cho phép edit trực tiếp


  // Check if there are unsaved tasks
  const checkUnsavedTasks = useCallback(() => {
    const currentScenario = scenarios.find(s => s._id === activeTabKey);
    if (!currentScenario || !currentScenario.taskIds) return [];

    const unsavedTasks = currentScenario.taskIds.filter(task => {
      if (typeof task === 'string') {
        // If it's a string ID, check if it's a temp ID
        return task.startsWith('temp_');
      }

      if (typeof task === 'object' && task !== null) {
        // Check if task has temp_id (indicating it's not saved yet)
        const taskId = task._id;
        return (
          !taskId ||
          taskId.startsWith('temp_')
          // Removed task.original_temp_id check because it indicates a task that WAS saved successfully
        );
      }

      return false;
    });

    console.log('Checking unsaved tasks:', {
      currentScenario: currentScenario?.name,
      totalTasks: currentScenario?.taskIds?.length || 0,
      unsavedTasks: unsavedTasks.length,
      unsavedTaskDetails: unsavedTasks.map(task => ({
        name: task.name || 'Unnamed',
        id: typeof task === 'object' ? task._id : task,
        isTemp: typeof task === 'object' ? task._id?.startsWith('temp_') : task?.startsWith('temp_'),
        hasOriginalTempId: typeof task === 'object' ? !!task.original_temp_id : false,
      })),
    });

    return unsavedTasks;
  }, [scenarios, activeTabKey]);


  // Filter roleplay instructions by simulation type
  const filteredRoleplayInstructions = useMemo(() => {
    console.log('Filtering roleplay instructions:', {
      total: roleplayInstructions.length,
      selectedSimulationType,
      instructions: roleplayInstructions.map(i => ({id: i._id, name: i.name, simulationType: i.simulationType})),
    });

    if (!selectedSimulationType) {
      return roleplayInstructions;
    }

    const filtered = roleplayInstructions.filter(
      instruction => instruction.simulationType === selectedSimulationType,
    );

    console.log('Filtered roleplay instructions:', filtered.length);
    return filtered;
  }, [roleplayInstructions, selectedSimulationType]);

  // Memoize unsaved tasks calculation
  const unsavedTasks = useMemo(() => {
    return checkUnsavedTasks();
  }, [checkUnsavedTasks, taskUpdateTrigger]);

  // Handle AI Persona creation success
  const handlePersonaCreated = (newPersona) => {
    setAiPersonas(prev => [...prev, newPersona]);
    setIsCreatePersonaModalOpen(false);

    // Auto-select the newly created persona
    const currentScenario = getCurrentScenario();
    if (currentScenario) {
      form.setFieldsValue({aiPersonaId: newPersona._id});
      handleAiPersonaChange(newPersona._id);
    }

    toast.success(t('AI_PERSONA_CREATED_SUCCESS'));
  };

  // Handle AI Persona edit
  const handlePersonaEdit = (persona) => {
    setEditingPersona(persona);
    setIsEditPersonaModalOpen(true);
  };

  // Handle AI Persona update success
  const handlePersonaUpdated = (updatedPersona) => {
    setAiPersonas(prev => prev.map(p => p._id === updatedPersona._id ? updatedPersona : p));
    setIsEditPersonaModalOpen(false);
    setEditingPersona(null);

    // Update selected persona for preview if it's the same one
    if (selectedPersonaForPreview && selectedPersonaForPreview._id === updatedPersona._id) {
      setSelectedPersonaForPreview(updatedPersona);
    }

    toast.success(t('AI_PERSONA_UPDATED_SUCCESS'));
  };

  const renderTabLabel = (scenario) => {
    // Không cho phép edit tên tab trực tiếp - chỉ hiển thị tên
    return (
      <div className="tab-label">
        <span>{scenario.name}</span>
      </div>
    );
  };

  const getCurrentScenario = () => {
    return scenarios.find(s => s._id === activeTabKey);
  };

  const getCurrentTasksList = () => {
    const currentScenario = getCurrentScenario();
    console.log('=== GET CURRENT TASKS LIST ===');
    console.log('Current scenario:', currentScenario);
    console.log('TaskIds:', currentScenario?.taskIds);
    console.log('TaskIds length:', currentScenario?.taskIds?.length);
    console.log('First task type:', currentScenario?.taskIds?.[0] ? typeof currentScenario.taskIds[0] : 'none');

    // Nếu taskIds là array of objects, trả về trực tiếp
    // Nếu taskIds là array of IDs, trả về mảng rỗng (sẽ được load từ API)
    if (currentScenario?.taskIds && Array.isArray(currentScenario.taskIds)) {
      if (currentScenario.taskIds.length > 0 && typeof currentScenario.taskIds[0] === 'object') {
        console.log('Returning task objects:', currentScenario.taskIds);
        return currentScenario.taskIds;
      }
    }
    console.log('Returning empty array');
    return [];
  };


  // Expose save method for parent component
  const saveAllScenarios = async () => {
    try {
      // Validate current form
      const formValues = await form.validateFields();
      console.log('Form values:', formValues);

      // Check for unsaved tasks in all scenarios và cảnh báo
      const allUnsavedTasks = [];
      scenarios.forEach((scenario, index) => {
        if (scenario.taskIds) {
          const scenarioUnsavedTasks = scenario.taskIds.filter(task => {
            return typeof task === 'object' && (
              task._id?.startsWith('temp_') ||
              !task._id
              // Removed task.original_temp_id check because it indicates a task that WAS saved successfully
            );
          });
          if (scenarioUnsavedTasks.length > 0) {
            allUnsavedTasks.push({
              scenarioName: scenario.name || `${t('SCENARIO')} ${index + 1}`,
              tasks: scenarioUnsavedTasks,
            });
          }
        }
      });

      // Cảnh báo về các task chưa lưu nhưng vẫn tiếp tục lưu scenario (chỉ lưu các task đã saved)
      if (allUnsavedTasks.length > 0) {
        const warningMessage = allUnsavedTasks.map(item =>
          `${item.scenarioName}: ${item.tasks.map(task => task.name || t('UNNAMED_TASK')).join(', ')}`,
        ).join('\n');

        console.warn('Unsaved tasks found, will be excluded from save:', warningMessage);
        // Không hiển thị toast warning ở đây vì đã có cảnh báo trong UI
        // Không return false, tiếp tục lưu với các task đã saved
      }

      // Save all scenarios that need saving
      const savePromises = scenarios.map(async (scenario) => {
        if (scenario.isNew || scenario.isModified) {
          // Ensure all taskIds are real IDs (not temp IDs)
          const validTaskIds = scenario.taskIds?.filter(task => {
            const taskId = typeof task === 'object' ? task._id : task;
            return taskId && !taskId.startsWith('temp_');
          }).map(task => typeof task === 'object' ? task._id : task) || [];

          const scenarioData = {
            ...scenario,
            courseId,
            // If this is the active tab, use form values
            ...(scenario._id === activeTabKey ? formValues : {}),
            // Use only valid task IDs
            taskIds: validTaskIds,
          };

          // Remove temporary fields
          delete scenarioData.isNew;
          delete scenarioData.isModified;

          console.log('Saving scenario:', scenarioData);
          console.log('Valid task IDs:', validTaskIds);

          if (scenario.isNew) {
            const response = await createAIScenario(scenarioData, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
            console.log('Created scenario response:', response);
            return response;
          } else {
            const response = await updateAIScenario(scenarioData, ['aiPersonaId', 'roleplayInstructionId', 'taskIds'], false);
            console.log('Updated scenario response:', response);
            return response;
          }
        }
        return scenario;
      });

      const results = await Promise.all(savePromises);
      console.log('Save results:', results);

      // Update scenarios state with saved data
      const updatedScenarios = scenarios.map((scenario, index) => {
        const result = results[index];
        if (result && result._id && result._id !== scenario._id) {
          // New scenario created - use data from server
          console.log('New scenario created:', result);
          return {...result, isNew: false, isModified: false};
        } else if (scenario.isModified && result) {
          // Existing scenario updated - merge with server data
          console.log('Existing scenario updated:', result);
          return {...result, isModified: false};
        }
        return scenario;
      });

      setScenarios(updatedScenarios);

      // Update active tab key if it was a new scenario
      const currentScenario = getCurrentScenario();
      if (currentScenario && currentScenario.isNew) {
        const newScenario = updatedScenarios.find(s => s.name === currentScenario.name && !s.isNew);
        if (newScenario) {
          setActiveTabKey(newScenario._id);
          console.log('Updated active tab key to:', newScenario._id);
        }
      }

      // Refresh form with updated data
      setTimeout(() => {
        const updatedCurrentScenario = updatedScenarios.find(s => s._id === activeTabKey);
        if (updatedCurrentScenario) {
          updateFormWithScenario(updatedCurrentScenario);
        }
      }, 100);

      // Notify parent component
      if (onScenariosChange) {
        onScenariosChange(updatedScenarios);
      }

      // Không hiển thị toast success ở đây vì parent component sẽ hiển thị thông báo tổng thể
      return true;
    } catch (error) {
      console.error('Error saving scenarios:', error);
      toast.error(t('SAVE_SCENARIOS_ERROR'));
      return false;
    }
  };

  // Expose this method to parent
  useImperativeHandle(ref, () => ({
    saveAllScenarios,
    checkUnsavedTasks,
  }));

  const tabItems = scenarios.map(scenario => ({
    key: scenario._id,
    label: renderTabLabel(scenario),
    closable: true, // Cho phép đóng tab (hiển thị nút X)
    children: (
      <div className="scenario-tab-content">
        <AntForm
          form={form}
          layout="vertical"
          onValuesChange={(changedValues) => {
            updateCurrentScenario(changedValues);
          }}
        >
          <Card
            title={t('SCENARIO_INFORMATION') }
            className="basic-info-card"
          >
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  name="name"
                  label={t('SCENARIO_NAME')}
                  rules={[
                    {required: true, message: t('PLEASE_ENTER_SCENARIO_NAME')},
                    {max: 100, message: t('SCENARIO_NAME_TOO_LONG')},
                  ]}
                >
                  <Input
                    placeholder={t('ENTER_SCENARIO_NAME')}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12}>
                <Form.Item
                  name="estimatedCallTimeInMinutes"
                  label={t('ESTIMATED_TIME_MINUTES')}
                  rules={[{required: true, message: t('PLEASE_ENTER_ESTIMATED_CALL_TIME')}]}
                >
                  <InputNumber
                    min={1}
                    max={120}
                    placeholder={t('ENTER_ESTIMATED_TIME')}
                    style={{width: '100%'}}
                    addonAfter={t('MINUTES')}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  name="roleplayInstructionId"
                  label={t('ROLEPLAY_INSTRUCTION')}
                  rules={[{required: true, message: t('PLEASE_SELECT_ROLEPLAY_INSTRUCTION')}]}
                >
                  <Select
                    placeholder={
                      !selectedSimulationType
                        ? t('PLEASE_SELECT_SIMULATION_TYPE_FIRST')
                        : filteredRoleplayInstructions.length === 0
                          ? t('NO_ROLEPLAY_INSTRUCTIONS_AVAILABLE')
                          : t('SELECT_ROLEPLAY_INSTRUCTION')
                    }
                    allowClear
                    showSearch
                    disabled={!selectedSimulationType}
                    filterOption={(input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                    notFoundContent={
                      !selectedSimulationType
                        ? t('PLEASE_SELECT_SIMULATION_TYPE_FIRST')
                        : t('NO_ROLEPLAY_INSTRUCTIONS_FOUND')
                    }
                  >
                    {filteredRoleplayInstructions.map(instruction => (
                      <Option key={instruction._id} value={instruction._id}>
                        {instruction.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

          </Card>

          {/* AI Configuration Section */}
          <Card
            title={t('AI_CONFIGURATION') }
            extra={
              <AntButton
                type={BUTTON.DEEP_NAVY}
                size="small"
                icon={<PlusOutlined />}
                onClick={() => setIsCreatePersonaModalOpen(true)}
              >
                {t('CREATE_NEW_AI_PERSONA')}
              </AntButton>
            }
            className="ai-config-card"
          >
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  name="aiPersonaId"
                  label={t('AI_PERSONA')}
                  rules={[{required: true, message: t('PLEASE_SELECT_AI_PERSONA')}]}
                >
                  <Select
                    placeholder={t('SELECT_AI_PERSONA')}
                    showSearch
                    allowClear
                    onChange={handleAiPersonaChange}
                    filterOption={(input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                    dropdownRender={menu => (
                      <div>
                        {menu}
                        <Divider style={{margin: '8px 0'}} />
                        <div className="persona-dropdown-footer">
                          <AntButton
                            type={BUTTON.DEEP_NAVY}
                            size="small"
                            icon={<PlusOutlined />}
                            onClick={() => setIsCreatePersonaModalOpen(true)}
                          >
                            {t('CREATE_NEW_AI_PERSONA')}
                          </AntButton>
                        </div>
                      </div>
                    )}
                  >
                    {aiPersonas.map(persona => (
                      <Option key={persona._id} value={persona._id}>
                        {persona.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            {/* AI Persona Preview - Professional Design */}
            {selectedPersonaForPreview && (
              <AIPersonaSetupCard
                persona={selectedPersonaForPreview}
                onEdit={() => handlePersonaEdit(selectedPersonaForPreview)}
                onLearnMore={() => {
                }}
              />
            )}
          </Card>

          {/* Tasks Management Section */}
          <Card
            title={
              <div className="tasks-header">
                {t('TASKS_MANAGEMENT')}
              </div>
            }
            extra={
              <Space>
                <AntButton
                  onClick={() => {
                    const newId = `temp_${Date.now()}`;
                    const newTask = {
                      _id: newId,
                      name: '',
                      description: '',
                      evaluationGuidelines: '',
                      weight: 0,
                      exampleVideoUrl: '',
                      helpfulLinks: [],
                      isMakeOrBreak: false,
                    };
                    const currentScenario = getCurrentScenario();
                    if (currentScenario) {
                      const updatedTaskIds = [...(currentScenario.taskIds || []), newTask];
                      updateCurrentScenario({taskIds: updatedTaskIds});
                      setTaskUpdateTrigger(prev => prev + 1);
                    }
                    if (onTaskAdd) onTaskAdd(newTask);
                  }}
                  size="small"
                  icon={<PlusOutlined />}
                  type={BUTTON.DEEP_NAVY}
                >
                  {t('ADD_TASK_TOPIC', 'Thêm chủ đề / nhiệm vụ')}
                </AntButton>
                <AntButton
                  onClick={() => {
                    // Trigger AI modal from TaskEvaluationDisplayCard
                    if (taskCardRef.current && taskCardRef.current.showAIModal) {
                      taskCardRef.current.showAIModal();
                    }
                  }}
                  icon={<RobotOutlined />}
                  disabled={!courseId}
                  type="primary"
                  style={{background: '#722ED1'}}
                  size="small"
                >
                  {t('CREATE_FROM_AI', 'Tạo từ AI')}
                </AntButton>
              </Space>
            }
            className="tasks-management-card"
          >

            <TaskEvaluationDisplayCard
              ref={taskCardRef}
              dataSource={getCurrentTasksList()}
              hideButtons={true}
              key={`tasks-${activeTabKey}-${taskUpdateTrigger}`} // Force re-render when tasks change
              onTaskAdd={(newTask) => {
                console.log('=== TASK ADD IN SCENARIO TABS ===');
                console.log('New task received:', newTask);
                const currentScenario = getCurrentScenario();
                console.log('Current scenario before update:', currentScenario);
                if (currentScenario) {
                  const currentTaskIds = currentScenario.taskIds || [];
                  console.log('Current taskIds:', currentTaskIds);
                  const updatedTaskIds = [...currentTaskIds, newTask];
                  console.log('Updated taskIds:', updatedTaskIds);

                  // Sử dụng updateCurrentScenario để thống nhất
                  updateCurrentScenario({taskIds: updatedTaskIds});
                  console.log('Scenario updated with new taskIds');

                  setTaskUpdateTrigger(prev => {
                    const newValue = prev + 1;
                    console.log('Task update trigger:', prev, '->', newValue);
                    return newValue;
                  });
                }
                if (onTaskAdd) onTaskAdd(newTask);
              }}
              onTaskUpdate={(updatedTask) => {
                console.log('Task updated:', updatedTask);
                const currentScenario = getCurrentScenario();
                if (currentScenario) {
                  const updatedTaskIds = (currentScenario.taskIds || []).map(task => {
                    // Handle case where task was created with temp_id and now has real ID
                    if (updatedTask.original_temp_id && task._id === updatedTask.original_temp_id) {
                      console.log('Replacing temp task with real task:', updatedTask);
                      // Remove original_temp_id as it's no longer needed
                      const {original_temp_id, ...cleanTask} = updatedTask;
                      return cleanTask;
                    }
                    // Handle normal update case
                    if (task._id === updatedTask._id) {
                      return updatedTask;
                    }
                    return task;
                  });
                  updateCurrentScenario({taskIds: updatedTaskIds});
                  console.log('Updated scenario taskIds after update:', updatedTaskIds);
                  setTaskUpdateTrigger(prev => prev + 1); // Force re-render
                }
                if (onTaskUpdate) onTaskUpdate(updatedTask);
              }}
              onTaskDelete={(taskId) => {
                console.log('Task deleted:', taskId);
                const currentScenario = getCurrentScenario();
                if (currentScenario) {
                  const updatedTaskIds = (currentScenario.taskIds || []).filter(task => {
                    const currentTaskId = typeof task === 'object' ? task._id : task;
                    return currentTaskId !== taskId;
                  });
                  updateCurrentScenario({taskIds: updatedTaskIds});
                  console.log('Updated scenario taskIds after delete:', updatedTaskIds);
                  setTaskUpdateTrigger(prev => prev + 1); // Force re-render
                }
                if (onTaskDelete) onTaskDelete(taskId);
              }}
              courseId={courseId}
            />
          </Card>

          {/* Unsaved tasks warning */}
          {unsavedTasks.length > 0 && (
            <Alert
              message={t('UNSAVED_TASKS_WARNING', {
                tasks: unsavedTasks.map(task => task.name || t('UNNAMED_TASK')).join(', '),
              })}
              description={t('PLEASE_SAVE_TASKS_BEFORE_SAVING_COURSE')}
              type="warning"
              showIcon
            />
          )}
        </AntForm>
      </div>
    ),
  }));

  return (
    <div className="ai-scenario-tabs">
      <Tabs
        type="editable-card"
        activeKey={activeTabKey}
        onChange={setActiveTabKey}
        onEdit={handleTabEdit}
        items={tabItems}
        addIcon={
          <Tooltip title={t('ADD_NEW_SCENARIO', 'Thêm kịch bản mới')}>
            <PlusOutlined />
          </Tooltip>
        }
        hideAdd={false}
      />

      {/* Modal tạo AI Persona */}
      <CreateAIPersonaModal
        isOpen={isCreatePersonaModalOpen}
        onClose={() => setIsCreatePersonaModalOpen(false)}
        onSuccess={handlePersonaCreated}
        courseId={courseId}
      />

      {/* Modal edit AI Persona */}
      <EditAIPersonaModal
        isOpen={isEditPersonaModalOpen}
        onClose={() => setIsEditPersonaModalOpen(false)}
        onSuccess={handlePersonaUpdated}
        persona={editingPersona}
      />
    </div>
  );
});

export default AIScenarioTabs;
